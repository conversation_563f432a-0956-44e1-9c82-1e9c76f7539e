<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
  <dict>
    <key>ITSAppUsesNonExemptEncryption</key>
    <false/>
    <key>LSSupportsOpeningDocumentsInPlace</key>
    <false/>
    <key>UIViewControllerBasedStatusBarAppearance</key>
    <false/>
    <key>UIHomeIndicatorAutoHidden</key>
    <true/>
    <key>UIRequiresFullScreen</key>
    <false/>
    <key>CFBundleDocumentTypes</key>
    <array>
      <dict>
        <key>CFBundleTypeName</key>
        <string>EPUB Document</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>org.idpf.epub-container</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>PDF Document</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.adobe.pdf</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>FB2 Document</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.readest.fb2</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>CBZ Archive</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.readest.cbz</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>MOBI Document</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>org.mobipocket.mobi</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>AZW Document</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>com.amazon.azw</string>
          <string>com.amazon.azw3</string>
        </array>
      </dict>

      <dict>
        <key>CFBundleTypeName</key>
        <string>Text File</string>
        <key>LSHandlerRank</key>
        <string>Alternate</string>
        <key>LSItemContentTypes</key>
        <array>
          <string>public.plain-text</string>
        </array>
      </dict>
    </array>

    <key>UTExportedTypeDeclarations</key>
    <array>
      <dict>
        <key>UTTypeIdentifier</key>
        <string>org.idpf.epub-container</string>
        <key>UTTypeDescription</key>
        <string>EPUB Document</string>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.data</string>
          <string>public.content</string>
        </array>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>epub</string>
          </array>
          <key>public.mime-type</key>
          <string>application/epub+zip</string>
        </dict>
      </dict>

      <dict>
        <key>UTTypeIdentifier</key>
        <string>com.readest.fb2</string>
        <key>UTTypeDescription</key>
        <string>FB2 Document</string>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.xml</string>
        </array>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>fb2</string>
          </array>
          <key>public.mime-type</key>
          <string>application/xml</string>
        </dict>
      </dict>

      <dict>
        <key>UTTypeIdentifier</key>
        <string>com.readest.cbz</string>
        <key>UTTypeDescription</key>
        <string>CBZ Archive</string>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.archive</string>
        </array>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>cbz</string>
          </array>
          <key>public.mime-type</key>
          <string>application/x-cbz</string>
        </dict>
      </dict>

      <dict>
        <key>UTTypeIdentifier</key>
        <string>org.mobipocket.mobi</string>
        <key>UTTypeDescription</key>
        <string>MOBI Document</string>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.data</string>
        </array>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>mobi</string>
          </array>
          <key>public.mime-type</key>
          <string>application/x-mobipocket-ebook</string>
        </dict>
      </dict>

      <dict>
        <key>UTTypeIdentifier</key>
        <string>com.amazon.azw</string>
        <key>UTTypeDescription</key>
        <string>AZW Document</string>
        <key>UTTypeConformsTo</key>
        <array>
          <string>public.data</string>
        </array>
        <key>UTTypeTagSpecification</key>
        <dict>
          <key>public.filename-extension</key>
          <array>
            <string>azw</string>
            <string>azw3</string>
          </array>
          <key>public.mime-type</key>
          <string>application/vnd.amazon.ebook</string>
        </dict>
      </dict>
    </array>
  </dict>
</plist>
