# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-all-voices"
description = "Enables the get_all_voices command without any pre-configured scope."
commands.allow = ["get_all_voices"]

[[permission]]
identifier = "deny-get-all-voices"
description = "Denies the get_all_voices command without any pre-configured scope."
commands.deny = ["get_all_voices"]
