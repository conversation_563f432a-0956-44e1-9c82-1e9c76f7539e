## Default Permission

Default permissions for the plugin

#### This default permission set includes the following:

- `allow-init`
- `allow-speak`
- `allow-stop`
- `allow-pause`
- `allow-resume`
- `allow-set-rate`
- `allow-set-pitch`
- `allow-set-voice`
- `allow-get-all-voices`
- `allow-registerListener`
- `allow-remove-listener`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`native-tts:allow-get-all-voices`

</td>
<td>

Enables the get_all_voices command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-get-all-voices`

</td>
<td>

Denies the get_all_voices command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-init`

</td>
<td>

Enables the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-init`

</td>
<td>

Denies the init command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-pause`

</td>
<td>

Enables the pause command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-pause`

</td>
<td>

Denies the pause command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-registerListener`

</td>
<td>

Enables the registerListener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-registerListener`

</td>
<td>

Denies the registerListener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-remove-listener`

</td>
<td>

Enables the remove_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-remove-listener`

</td>
<td>

Denies the remove_listener command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-resume`

</td>
<td>

Enables the resume command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-resume`

</td>
<td>

Denies the resume command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-set-pitch`

</td>
<td>

Enables the set_pitch command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-set-pitch`

</td>
<td>

Denies the set_pitch command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-set-rate`

</td>
<td>

Enables the set_rate command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-set-rate`

</td>
<td>

Denies the set_rate command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-set-voice`

</td>
<td>

Enables the set_voice command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-set-voice`

</td>
<td>

Denies the set_voice command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-speak`

</td>
<td>

Enables the speak command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-speak`

</td>
<td>

Denies the speak command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:allow-stop`

</td>
<td>

Enables the stop command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-tts:deny-stop`

</td>
<td>

Denies the stop command without any pre-configured scope.

</td>
</tr>
</table>
