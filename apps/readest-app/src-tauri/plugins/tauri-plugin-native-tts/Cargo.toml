[package]
name = "tauri-plugin-native-tts"
version = "0.1.0"
authors = [ "You" ]
description = ""
edition = "2021"
rust-version = "1.77.2"
exclude = ["/examples", "/dist-js", "/guest-js", "/node_modules"]
links = "tauri-plugin-native-tts"

[dependencies]
tauri = { version = "2" }
serde = "1.0"
thiserror = "2"
schemars = "0.8"

[build-dependencies]
tauri-plugin = { version = "2", features = ["build"] }
schemars = "0.8"
