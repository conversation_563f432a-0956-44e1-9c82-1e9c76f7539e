# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-get-system-color-scheme"
description = "Enables the get_system_color_scheme command without any pre-configured scope."
commands.allow = ["get_system_color_scheme"]

[[permission]]
identifier = "deny-get-system-color-scheme"
description = "Denies the get_system_color_scheme command without any pre-configured scope."
commands.deny = ["get_system_color_scheme"]
