# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-iap-restore-purchases"
description = "Enables the iap_restore_purchases command without any pre-configured scope."
commands.allow = ["iap_restore_purchases"]

[[permission]]
identifier = "deny-iap-restore-purchases"
description = "Denies the iap_restore_purchases command without any pre-configured scope."
commands.deny = ["iap_restore_purchases"]
