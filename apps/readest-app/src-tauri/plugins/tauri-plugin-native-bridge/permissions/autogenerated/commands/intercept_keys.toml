# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-intercept-keys"
description = "Enables the intercept_keys command without any pre-configured scope."
commands.allow = ["intercept_keys"]

[[permission]]
identifier = "deny-intercept-keys"
description = "Denies the intercept_keys command without any pre-configured scope."
commands.deny = ["intercept_keys"]
