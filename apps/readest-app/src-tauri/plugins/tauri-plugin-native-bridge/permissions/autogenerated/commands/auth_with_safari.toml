# Automatically generated - DO NOT EDIT!

"$schema" = "../../schemas/schema.json"

[[permission]]
identifier = "allow-auth-with-safari"
description = "Enables the auth_with_safari command without any pre-configured scope."
commands.allow = ["auth_with_safari"]

[[permission]]
identifier = "deny-auth-with-safari"
description = "Denies the auth_with_safari command without any pre-configured scope."
commands.deny = ["auth_with_safari"]
