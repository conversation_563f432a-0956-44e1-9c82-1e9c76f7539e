## Default Permission

Default permissions for the plugin

#### This default permission set includes the following:

- `allow-auth-with-safari`
- `allow-auth-with-custom-tab`
- `allow-copy-uri-to-path`
- `allow-use-background-audio`
- `allow-install-package`
- `allow-set-system-ui-visibility`
- `allow-get-status-bar-height`
- `allow-get-sys-fonts-list`
- `allow-intercept-keys`
- `allow-lock-screen-orientation`
- `allow-iap-initialize`
- `allow-iap-fetch-products`
- `allow-iap-purchase-product`
- `allow-iap-restore-purchases`
- `allow-get-system-color-scheme`

## Permission Table

<table>
<tr>
<th>Identifier</th>
<th>Description</th>
</tr>


<tr>
<td>

`native-bridge:allow-auth-with-custom-tab`

</td>
<td>

Enables the auth_with_custom_tab command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-auth-with-custom-tab`

</td>
<td>

Denies the auth_with_custom_tab command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-auth-with-safari`

</td>
<td>

Enables the auth_with_safari command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-auth-with-safari`

</td>
<td>

Denies the auth_with_safari command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-copy-uri-to-path`

</td>
<td>

Enables the copy_uri_to_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-copy-uri-to-path`

</td>
<td>

Denies the copy_uri_to_path command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-get-status-bar-height`

</td>
<td>

Enables the get_status_bar_height command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-get-status-bar-height`

</td>
<td>

Denies the get_status_bar_height command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-get-sys-fonts-list`

</td>
<td>

Enables the get_sys_fonts_list command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-get-sys-fonts-list`

</td>
<td>

Denies the get_sys_fonts_list command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-get-system-color-scheme`

</td>
<td>

Enables the get_system_color_scheme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-get-system-color-scheme`

</td>
<td>

Denies the get_system_color_scheme command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-iap-fetch-products`

</td>
<td>

Enables the iap_fetch_products command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-iap-fetch-products`

</td>
<td>

Denies the iap_fetch_products command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-iap-initialize`

</td>
<td>

Enables the iap_initialize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-iap-initialize`

</td>
<td>

Denies the iap_initialize command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-iap-purchase-product`

</td>
<td>

Enables the iap_purchase_product command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-iap-purchase-product`

</td>
<td>

Denies the iap_purchase_product command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-iap-restore-purchases`

</td>
<td>

Enables the iap_restore_purchases command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-iap-restore-purchases`

</td>
<td>

Denies the iap_restore_purchases command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-install-package`

</td>
<td>

Enables the install_package command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-install-package`

</td>
<td>

Denies the install_package command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-intercept-keys`

</td>
<td>

Enables the intercept_keys command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-intercept-keys`

</td>
<td>

Denies the intercept_keys command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-lock-screen-orientation`

</td>
<td>

Enables the lock_screen_orientation command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-lock-screen-orientation`

</td>
<td>

Denies the lock_screen_orientation command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-set-system-ui-visibility`

</td>
<td>

Enables the set_system_ui_visibility command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-set-system-ui-visibility`

</td>
<td>

Denies the set_system_ui_visibility command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:allow-use-background-audio`

</td>
<td>

Enables the use_background_audio command without any pre-configured scope.

</td>
</tr>

<tr>
<td>

`native-bridge:deny-use-background-audio`

</td>
<td>

Denies the use_background_audio command without any pre-configured scope.

</td>
</tr>
</table>
