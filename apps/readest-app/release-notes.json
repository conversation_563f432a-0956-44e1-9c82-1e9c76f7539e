{"releases": {"0.9.72": {"date": "2025-08-18", "notes": ["Fixed Edge TTS voice playback for EPUBs", "Reduced accidental page flips when toggling toolbars", "Fixed images with background colors not displaying correctly in some books", "Added support for custom KOReader Sync Servers on your local network (LAN)"]}, "0.9.71": {"date": "2025-08-13", "notes": ["Sync: Added two ways to sync reading progress with KOReader devices", "EPUB: Applied monospace font settings", "EPUB: Fixed initial text alignment for some EPUB files", "PDF: Fixed issue opening large PDF files on Android", "TTS: Improved book language detection when language code is invalid", "iOS: Fixed applying system color scheme in auto theme mode", "Config: Added option to choose reading progress display style (percentage or page number)", "Library: Added option to delete only the local copy of a book", "Account: Added 'Reset Password' button in the account page"]}, "0.9.69": {"date": "2025-08-02", "notes": ["Updater: Fixed permission issue when updating in standalone reader window", "Config: Added option to disable double‑click actions", "EPUB: Fixed missing images in some EPUB files", "EPUB: Corrected formatting for book subjects", "TXT: Improved language detection when parsing TXT files", "CSS: Resolved issue where custom font colors could not be applied", "Layout: Corrected cover image width display in 'Fit' mode for list view", "Layout: Fixed unintended scrolling in reader view when highlighting text"]}, "0.9.68": {"date": "2025-07-30", "notes": ["Reader: Files now open in a new window by default on Desktop", "Translation: Skips translating content inside <pre>, <code>, and <math> tags", "Performance: Improved multi-part downloading for large books", "Sync: Resolved issue preventing some PDF files from syncing", "CSS: Resolved issue with fixed font color in some EPUB files", "TXT: Enhanced TXT parsing for better compatibility"]}, "0.9.67": {"date": "2025-07-22", "notes": ["Reader: Fixed file opening issue when filenames contain commas", "Translation: Added Yandex Translator as a new translation provider", "Layout: Hovering over header now reveals macOS traffic light window controls", "Theme: Reader background color is now customizable for EPUB and PDF", "Library: Search now includes book format, group names, and descriptions", "Library: Various improvements to metadata editor and bookshelf management", "Linux: Fixed social login failure on Linux systems"]}, "0.9.66": {"date": "2025-07-21", "notes": ["Layout: Fixed nested table of contents items not expanding in long TOC lists", "Reader: Opening files from the OS now takes highest priority", "TTS: Improved voice filtering for some language codes", "App: Fixed loading issues on older browsers", "App: Fixed wrong MIME types generated on Linux"]}, "0.9.65": {"date": "2025-07-19", "notes": ["Reader: Added support for opening multiple reader windows on desktop", "Layout: Added visible window borders on Linux", "TTS: Improved handling of invalid book language", "TTS: Skipped reading footnote anchor links", "Translation: Fixed an issue where table of contents entries were not translated", "Sync: Edited book metadata is now included in sync across devices", "iOS: Disabled context menu when long-pressing on book covers"]}, "0.9.64": {"date": "2025-07-16", "notes": ["Library: Added support to edit book metadata", "Layout: Fixed bookmark icon placement when the sidebar is pinned", "Reader: Fixed an issue where links to other chapters in EPUB files did not work", "Reader: Lightened the highlight color in dark mode for better contrast", "Reader: Pull-to-refresh gesture is now smoother and more responsive", "Reader: Removed unintended indentation on images", "Search: Improved responsiveness of full-text search for faster results"]}, "0.9.63": {"date": "2025-07-07", "notes": ["Reader: Fixed links not working in some MOBI books", "TTS: Fixed an issue where some text was skipped during reading", "Library: You can now delete only the cloud backup of a book without removing the local copy", "Performance: Fonts in the font list now load only when displayed for faster startup", "Performance: Opening large MOBI books is now significantly faster", "Layout: Fixed overlay scrollbar display in longer TOC items list", "Layout: Improved grid cover image height in crop mode", "Layout: Removed unsupported screen orientation lock option on iPad", "Layout: System context menu no longer appears when selecting text on iPad", "Translation: Added Thai (th-TH) language support"]}, "0.9.62": {"date": "2025-07-02", "notes": ["Subscription: You can now upgrade to Readest Premium and manage your subscription", "Reader: Added an option to toggle Parallel Reading when viewing multiple books", "Translation: Added an option to show or hide the original text in translations", "Layout: TOC on Android now uses an overlay scrollbar for easier navigation", "Layout: Custom CSS can now also be applied to the library page", "Fonts: Added three new CJK fonts to choose from", "Shortcuts: Press ESC to quickly close the search bar"]}, "0.9.61": {"date": "2025-06-26", "notes": ["Layout: Fixed view menu width on smaller screens", "Layout: Added option to crop or fit book covers in library view", "Reader: You can now apply custom CSS to the reading interface (UI)", "Reader: Added an option to show remaining pages in the current chapter", "Settings: Added options to reset settings from the config dialog", "Import: Fixed encoded filenames when importing TXT files on iOS", "Linux: Fixed missing app icon on some Linux distributions", "GitHub: Added donation link to support Readest development"]}, "0.9.60": {"date": "2025-06-22", "notes": ["Layout has been improved for iPad devices", "Screen orientation changes are now smoother on iOS", "Importing books on iOS during the first launch is now more reliable", "Font size no longer changes unexpectedly in scrolled mode on iOS", "Top and bottom safe insets are now ignored when no header or footer is present", "Interactive books now load correctly on the Desktop platform", "Translation is now disabled when the primary language is undefined", "Android: the task list background color has been updated with current theme color"]}, "0.9.59": {"date": "2025-06-19", "notes": ["Layout: Image size now adjusts more accurately by converting screen units to pixels properly", "Layout: Improved header layout on iOS landscape mode — extra spacing has been removed", "Settings: Top and bottom margin settings are now more intuitive and better constrained"]}, "0.9.58": {"date": "2025-06-18", "notes": ["TTS: Translation now works even when T<PERSON> is playing in the background", "TTS: Press T on your keyboard to quickly toggle TTS on or off", "TTS: Each open book now has its own independent TTS controller", "Annotations: Annotation tools now function properly while TTS is active", "Settings: Margins for top, bottom, left, and right can now be adjusted individually", "Settings: New option to always show the status bar while reading", "Layout: Reader page now adapts better to device safe areas for a more comfortable layout", "UI: Added creation timestamps to annotations and made various interface improvements", "iPad: Multiple columns are now supported in portrait mode", "iPad: Fixed issue where sidebars couldn't be resized by dragging", "iPad: Added support for split-screen mode"]}, "0.9.57": {"date": "2025-06-14", "notes": ["TTS: Fixed an issue to detect custom TTS engines on some Android devices"]}, "0.9.56": {"date": "2025-06-13", "notes": ["Performance: Improved TOC loading for books with very large chapter lists", "TTS: Added support for native Android TTS engines (e.g., MultiTTS)", "TTS: Fixed an issue where the next chapter would not load properly in the background", "Translation: Improved accuracy and clarity of some Arabic translations", "Reading: Added support for code syntax highlighting in books with code snippets"]}, "0.9.55": {"date": "2025-06-07", "notes": ["Sync: Notes deleted on other devices now correctly sync across all platforms", "Layout: Now text justification can be unset in the layout settings", "Translation: Added notification when daily translation quota is exceeded", "Performance: Reduced unnecessary animations on Android to improve efficiency"]}, "0.9.53": {"date": "2025-06-05", "notes": ["Reader: New option to customize book foreground and background colors", "Reader: Fixed an issue where images appeared too small in some EPUBs", "EPUB: Improved cover image detection for certain EPUB files", "TTS: Improved background playback stability on iOS devices", "Translation: Introduced a daily DeepL translation quota to ensure long-term availability"]}, "0.9.52": {"date": "2025-06-03", "notes": ["Library: The search bar now shows the number of books currently displayed", "Book Details: Book descriptions now support rich HTML formatting", "Notebook: Added support for writing notes using Markdown", "Notebook: You can now search within your notes", "Sync: A sync status item has been added to the View menu", "TOC: You can now sort the table of contents by page number", "Reader: Option added to show remaining time in the current chapter"]}, "0.9.51": {"date": "2025-06-01", "notes": ["Translator: Table of contents is now included in translations", "Library Theme: Added light, dark, and system theme options to the library page", "TTS: Now shows the current sentence and chapter in the media session", "TTS: Text-to-speech continues from the last read sentence", "Pagination Fix: Taking notes no longer breaks page turning", "EPUB Scripts: Added a setting to allow JavaScript in EPUB files"]}, "0.9.50": {"date": "2025-05-28", "notes": ["Bilingual TTS: You can now choose voices for each language in bilingual text-to-speech mode", "Settings Layout: Improved header layout in the settings dialog for different screen sizes", "CSS Fixes: Corrected anchor text color and fixed image dimensions within paragraphs"]}, "0.9.49": {"date": "2025-05-27", "notes": ["Select All in Library: You can now select all books at once when in selection mode", "More Translation Options: Added support for Azure and Google Translate services", "Dark Mode Enhancement: New option to invert image colors for better visibility in dark mode", "Bilingual TTS: Enjoy bilingual text-to-speech for books with content in two languages", "Full Book Translation: Translate books in any language into bilingual ebooks", "Privacy Settings: Added a new option to opt-out of telemetry"]}, "0.9.43": {"date": "2025-05-19", "notes": ["Enhanced compatibility with Gutenberg and Feedbooks ebooks for better styling", "Reduced sensitivity of trackpad and mouse during page flips", "Cloud backup status is now visible for each book on mobile and desktop", "New option to control scroll overlap when paginating in scroll mode", "Added Dutch language translations", "Added more font weight options for built-in fonts", "Added previous and next section buttons in the footer bar for easier navigation"]}, "0.9.41": {"date": "2025-05-13", "notes": ["Improved UI and layout on mobile devices", "Avatar images are now cached for offline use", "Book file size is now displayed in the book details view", "Update status is now correctly shown in the About window", "TTS control positioning improved for notebook and RTL modes", "TXT files are segmented by paragraph count if no chapter titles are found", "Android: App now respects system auto-rotation settings", "Linux: Updater is disabled on Linux when not using AppImage", "Linux: Added support for Linux ARMHF builds", "CSS: Resolved layout and color issues caused by hardcoded CSS"]}, "0.9.40": {"date": "2025-05-08", "notes": ["Prevented oversized images from causing layout issues", "Resolved issue with transient navigation bar on Android 9", "Action bar now closes properly when the footbar is dismissed", "Smoother experience when expanding Table of Contents sections", "Reverted unintended style overrides"]}, "0.9.39": {"date": "2025-05-07", "notes": ["Enhanced Navigation: Added location information to the table of contents", "Enhanced Navigation: Larger touch targets for table of contents expansion icons", "Screen Orientation Lock: You can now lock your device's screen orientation when reading", "Quick Access to Recent Books: Option to automatically open your last read book when starting the app", "Better Language Support: Improved handling of CJK characters in search", "Enhanced Text-to-Speech: More accurate language detection for TTS", "Enhanced Text-to-Speech: Text-to-Speech now maintains your position in scroll mode", "Enhanced Annotations: Better handling of annotations with ruby elements in Japanese books", "Smoother Scrolling: We've made continuous scrolling more reliable for a seamless reading experience", "Compact View: Reading view is more compact when header and footer are hidden", "TXT Parsing: More robust handling of TXT book format"]}, "0.9.38": {"date": "2025-04-29", "notes": ["Page Turning with Volume Keys: You can now turn pages using your device’s volume buttons", "Expanded English Voices: More English voices are available across all en locales", "Theme Editor: Added a primary color picker for easier customization", "System Fonts: Retrieve system fonts on iOS and Android, with improved font weight and style detection", "Popup Footnotes: Popup footnotes now inherit the book's font for consistent appearance", "Better Text Selection: Preserves text selection anchors when spanning paginated content", "Android Compatibility: Navigation bar auto-hides correctly on Android 10 and below", "Popup Dialogs: Back key now properly dismisses popup dialogs on Android"]}, "0.9.37": {"date": "2025-04-25", "notes": ["Full-Screen Toggle: You can now press F11 to quickly toggle full-screen mode", "List View: A new list layout is available for your bookshelf", "Book Details: Book descriptions from metadata are now shown in the details view", "Enhanced Android behavior: better navigation bar handling and status bar dismissal when resuming", "TTS now defaults to the book’s metadata language when needed", "Synced custom themes reliably across sessions"]}, "0.9.36": {"date": "2025-04-20", "notes": ["📥 New download and upload buttons in the book detail dialog for easier file management", "📂 You can now open files directly in Readest from your file manager on Android and iOS", "🛠️ Fixed an issue where files with quotation marks in the filename could not be opened", "📖 Improved the immersive reading experience on the reader page for Android and iOS", "🌐 Added GuanKiapTsing<PERSON>hai-T to the list of available CJK fonts"]}, "0.9.35": {"date": "2025-04-15", "notes": ["🛠️ Fixed an issue where books without cover images can not be synced across devices", "📱 Added in-app update support for Android", "📚 You can now sort books by title or author in your library"]}, "0.9.33": {"date": "2025-04-13", "notes": ["New: Redesigned update dialog with a progress bar during downloads for a smoother update experience.", "New: You can now set header and footer visibility separately for paginated and scroll modes.", "Fixed layout issues in the bottom configuration panel when using RTL (right-to-left) languages.", "Fixed TXT file import issues on Android by improving support for content provider file paths.", "Fixed transformed punctuations when highlighting text with vertical layout.", "Improved login flow on macOS by handling OAuth with the native ASWebAuthenticationSession."]}, "0.9.32": {"date": "2025-04-09", "notes": ["No more resetting of font size and color when you apply custom book styles", "The underline and squiggly highlight decorations have been adjusted to sit neatly between text lines", "With more font options available, the font menu is no longer forced in-line", "LXGW WenKai TC is now part of our CJK font list"]}, "0.9.31": {"date": "2025-04-08", "notes": ["iOS: Text-to-Speech (TTS) now works in the background", "Android: Import files from native file manager", "Desktop: Add option to keep window on top", "Vertical layout for highlighting tools", "Added options to auto-stop TTS after timeout", "Added book search functionality in bookshelf", "Improved font support for CJK users"]}, "0.9.30": {"date": "2025-04-05", "notes": ["Added fixed storage quota when running the app in self-hosted mode", "Link colors will only change when you override fonts", "Added cache for native files for better performance"]}, "0.9.29": {"date": "2025-04-02", "notes": ["Improved Login on Android without needing to rely on external Chrome browser", "Local large book files now load more efficiently on Android", "Balanced the load of online service between different types of users to keep things running smoothly for everyone"]}, "0.9.28": {"date": "2025-03-30", "notes": ["Improved vertical reading mode with better support for Chinese punctuations", "Fixed TXT file importing issue on desktop apps — TXT books now load reliably and display as expected", "Improved annotation experience — highlighting tools are now properly dismissed when adding notes to avoid UI conflicts"]}, "0.9.27": {"date": "2025-03-29", "notes": ["Added option to swap click-to-flip area and reset password", "Added global shortcuts for full-text search", "Added support for both R2 and S3 storage in cloud sync", "Improved TTS with fewer unintended pauses, faster startup, and support for preferred voices", "Various fixes for rendering styles and UI layout"]}, "0.9.26": {"date": "2025-03-27", "notes": ["Support open book from file manager without importing", "Add LXGW WenKai font in Serif fonts list", "Various fixes on layout and styles on Windows and Android"]}, "0.9.25": {"date": "2025-03-24", "notes": ["Support exporting annotations in markdown format", "Support importing TXT files", "Support setting reader language in the UI", "Add bottom action bar on mobile platforms", "Various fixes on layout settings and styles"]}, "0.9.23": {"date": "2025-03-19", "notes": ["Add options to show/hide header and footer", "Add responsive window size for popup footnotes", "Fix theme color not applied on Safari browsers", "Fix progress not updated to 100% when finished reading"]}, "0.9.22": {"date": "2025-03-16", "notes": ["Add theme color editor for more customization", "Add border frame for vertical layout", "Fix layout glitches on macOS in scroll mode", "Various fixes and enhancements on translator, footnote, and scrollbar"]}, "0.9.21": {"date": "2025-03-10", "notes": ["Fix column height in vertical layout on mobile", "Fix drag handle height not constant on mobile", "Add fullscreen option on desktop", "Add drag and drop to import books on desktop", "Various fixes and enhancements on updater, footerbar and note"]}, "0.9.20": {"date": "2025-03-10", "notes": ["Fix column height in vertical layout on mobile", "Fix drag handle height not constant on mobile", "Add fullscreen option on desktop", "Add drag and drop to import books on desktop", "Various fixes and enhancements on updater, footerbar and note"]}, "0.9.19": {"date": "2025-03-07", "notes": ["Support custom CSS for Reader UI", "Initial support of RTL layout for Arabic and Hebrew books", "Various fixes and enhancements on layout and sync"]}, "0.9.18": {"date": "2025-02-26", "notes": ["Add user profile page and option to delete account in the cloud", "Fix TTS failed to start with invalid book language", "Enhancements on iOS with modals and annotation tools", "UX enhancements with modern highlights colors"]}, "0.9.17": {"date": "2025-02-24", "notes": ["Add Arabic localization", "Add <PERSON><PERSON><PERSON><PERSON> to selfhost", "Add safari-auth plugin for iOS OAuth", "UX enhancements for mobile platforms with haptics feedback and pull-down dismissal"]}, "0.9.15": {"date": "2025-02-21", "notes": ["Fix hardcoded font color from calibre", "Fix scroll to current TOC item when toggling sidebar", "Various fixes and enhancements on iOS platform"]}, "0.9.13": {"date": "2025-02-19", "notes": ["Add option to keep screen awake when reading", "Add shortcuts to notetaking UI", "UX enhancements of grid view in mobile platforms"]}, "0.9.11": {"date": "2025-02-14", "notes": ["Organize the library with customizable groups/folders", "Alpha release for Android and iOS platforms", "Various fixes and enhancements on Android and iOS platforms"]}, "0.9.10": {"date": "2025-02-08", "notes": ["Add options to adjust paragraph margin, word spacing, letter spacing, text indent and font weight", "Alpha release for Android and iOS platforms"]}, "0.9.9": {"date": "2025-02-06", "notes": ["Load system fonts list at runtime on desktop", "Incrementally load new books in synchronization", "Change voice language dynamically by detecting text language", "Various fixes and enhancements on sync, fonts and highlighting"]}, "0.9.8": {"date": "2025-02-01", "notes": ["Support cloud sync for book files", "Use larger font size on mobile devices for better reading experience", "Various fixes and enhancements on sync, rendering and settings"]}, "0.9.7": {"date": "2025-01-23", "notes": ["Support PWA for the web version", "Swipe up to toggle header/footer", "Various fixes and enhancements on updater, header bar and tts"]}, "0.9.6": {"date": "2025-01-18", "notes": ["Various enhancements for mobile platforms", "Fix DeepL translation API"]}, "0.9.5": {"date": "2025-01-15", "notes": ["Various TTS related UX enhancements", "Add more system fonts as custom fonts", "Show reading progress in bookshelf"]}, "0.9.3": {"date": "2025-01-11", "notes": ["Hotfix occasionally current speak not stopped when changing voice or rate", "Inherit tts rate when changing tts engine"]}, "0.9.2": {"date": "2025-01-10", "notes": ["Support Text-to-Speech with Edge TTS and Web Speech API.", "Various fixes and enhancements."]}, "0.9.1": {"date": "2025-01-06", "notes": ["Restore window position and size when reopening app.", "Swap font when downloading online fonts.", "Fix layout issues on Windows and Linux."]}, "0.9.0": {"date": "2025-01-04", "notes": ["Better Custom CSS editor and OAuth process.", "Add vertical/horizontal switch for CJK books.", "Fix dropdown close overlay not working on Windows/Linux."]}, "0.8.9": {"date": "2025-01-02", "notes": ["Context menu on bookshelf for quick actions.", "Add more system fonts as custom fonts.", "Fix google and github login for native apps."]}, "0.8.8": {"date": "2024-12-29", "notes": ["Use readest binary name for better CLI interface.", "Some CSS tweaks to better support dark mode and embedded fonts."]}, "0.8.7": {"date": "2024-12-27", "notes": ["Support i18n/l10n with popular languages.", "Support information modal for book details.", "Various fixes and enhancements."]}, "0.8.6": {"date": "2024-12-25", "notes": ["Support back navigation in login page.", "Support context menu on book cover for desktop version.", "Resolve the issue with restoring footnotes and bookmarks after deletion."]}, "0.8.5": {"date": "2024-12-24", "notes": ["Support progress and notes sync across devices.", "Support for Non-ASCII characters in custom CSS.", "Support to disable click-to-flip."]}, "0.8.3": {"date": "2024-12-12", "notes": ["Support popover footnotes.", "Support left / right popover on vertical writing documents.", "Support loading additional fonts."]}, "0.8.2": {"date": "2024-12-06", "notes": ["Support file associations for one-click open with Readest.", "Support APP auto updater.", "Support online web version."]}, "0.7.9": {"date": "2024-12-01", "notes": ["Initial release with full functionality for ePub rendering.", "Support multi-book reading with <PERSON><PERSON><PERSON> Read."]}}}