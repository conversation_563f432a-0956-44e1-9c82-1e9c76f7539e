@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  border-radius: 10px;
  scrollbar-gutter: auto !important;
  overscroll-behavior: none;
  background-color: transparent;

  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
}

/* Fixes the scrollbar gutter issue for DaisyUI */
/* https://github.com/saadeghi/daisyui/issues/3040#issuecomment-2250530354 */
:root:has(
    :is(
        .modal-open,
        .modal:target,
        .modal-toggle:checked + .modal,
        .modal[open]
      )
  ) {
  scrollbar-gutter: unset !important;
}

html[data-page='default'] {
  background: theme('colors.base-100');
}

html[data-page='library'] {
  background: theme('colors.base-200');
}

html[data-page='reader'] {
  background: theme('colors.base-100');
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial,
    sans-serif;
  border-radius: 10px;
  background-color: transparent;
  cursor: default;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

foliate-view {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
}

@keyframes scaleUpAndDown {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.hover-bar-anim:hover ~ .foliate-viewer {
  animation: scaleUpAndDown 0.3s ease forwards;
}

.window-button {
  @apply inline-flex h-6 w-6 items-center justify-center rounded-full;
  @apply transform transition duration-200 ease-in-out hover:scale-105;
  @apply bg-base-200/85 hover:bg-base-200;
}

.rounded-window {
  border-radius: 10px;
}
.rounded-window-top-left {
  border-top-left-radius: 10px;
}
.rounded-window-bottom-left {
  border-bottom-left-radius: 10px;
}
.rounded-window-top-right {
  border-top-right-radius: 10px;
}
.rounded-window-bottom-right {
  border-bottom-right-radius: 10px;
}

.window-border {
  border-radius: 10px;
  z-index: 999;
  position: relative;
}

.window-border::before {
  content: '';
  z-index: 1000;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1px solid theme('colors.base-300');
  border-radius: 10px;
  pointer-events: none;
}

.dropdown-content {
  position: relative;
  @apply bg-base-100;
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  border-radius: 14px;
}

.dropdown-content::before,
.dropdown-content::after {
  content: '';
  position: absolute;
  top: -12px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
}

.dropdown-content::before {
  border-bottom: 12px solid theme('colors.base-100');
  z-index: 1;
}

.dropdown-content::after {
  top: -14px;
  border-bottom: 13px solid rgba(0, 0, 0, 0.02);
  z-index: 0;
}

.dropdown-content.bgcolor-base-200 {
  background-color: theme('colors.base-200');
}
.dropdown-content.bgcolor-base-200::before {
  border-bottom: 12px solid theme('colors.base-200');
  z-index: 1;
}

.dropdown-left::before,
.dropdown-left::after {
  left: 20px;
  transform: translateX(0);
}

.dropdown-center::before,
.dropdown-center::after {
  left: 50%;
  transform: translateX(-50%);
}

.dropdown-right::before,
.dropdown-right::after {
  right: 20px;
  transform: translateX(0);
}

.dropdown-content.no-triangle::before,
.dropdown-content.no-triangle::after {
  display: none;
}

.tooltip.no-triangle::before,
.tooltip.no-triangle::after,
.tooltip-top.no-triangle::before,
.tooltip-top.no-triangle::after,
.tooltip-bottom.no-triangle::before,
.tooltip-bottom.no-triangle::after {
  display: none;
}

.dropdown-content,
.settings-content {
  font-size: 14px;
  font-weight: 400;
}

@media (max-width: 768px) {
  .dropdown-content,
  .settings-content {
    font-size: 16px;
  }
}

.config-item {
  @apply flex h-14 items-center justify-between p-4;
  @apply hover:bg-base-100/50;
}

.collapse-arrow > .collapse-title:after {
  top: var(--top-override, default-value);
  inset-inline-end: var(--end-override, default-value);
}

.search-bar {
  max-height: 0;
  visibility: hidden;
  overflow: visible;
  transition: max-height 0.3s ease-out;
}

.search-bar-visible {
  max-height: 48px;
  visibility: visible;
}

.pull-indicator {
  position: fixed;
  width: 100%;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-text-lighter);
  transition: transform 0.2s ease-in-out;
  z-index: 10;
}

.pull-indicator svg {
  transition: transform 0.2s ease-in-out;
}

.pull-indicator.flip svg {
  transform: rotate(180deg);
}

.content {
  font-size: 16px;
}

@media (max-width: 480px) {
  .content {
    font-size: 20px;
  }
}

@media (min-width: 481px) and (max-width: 1024px) {
  .content {
    font-size: 18.4px;
  }
}

.content.font-size-base {
  font-size: 1em !important;
  line-height: 1.5em !important;
}

.content.font-size-sm {
  font-size: 0.875em !important;
  line-height: 1.25em !important;
}

.content.font-size-xs {
  font-size: 0.75em !important;
  line-height: 1em !important;
}

.writing-vertical-rl {
  writing-mode: vertical-rl;
}

.direction-rtl {
  direction: rtl;
}

.drop-zone {
  position: relative;
}

.drag-over {
  border: 2px dashed #4a90e2 !important;
  background-color: rgba(74, 144, 226, 0.1) !important;
  transition: all 0.2s ease-in-out;
}

.drag-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 20;
  pointer-events: none;
}

.drop-zone.drag-over .drag-overlay {
  display: block;
}

.drop-indicator {
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 50;
  padding: 20px;
  background-color: theme('colors.base-300');
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  pointer-events: none;
}

.drop-zone.drag-over .drop-indicator {
  display: block;
}

.no-context-menu {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
}

.book-spine {
  background-image:
    linear-gradient(180deg,
      hsla(0, 0%, 95%, 0.2) 0%,    /* Top highlight */
      hsla(0, 0%, 90%, 0.1) 1%,    /* Top highlight */
      transparent 2%,                /* Main book surface */
      transparent 50%,               /* Main book surface */
      hsla(0, 0%, 25%, 0.15) 95%,  /* Bottom shadow */
      hsla(0, 0%, 15%, 0.2) 100%   /* Bottom edge dark */
    ),
    linear-gradient(90deg,
      hsla(0, 0%, 0%, 0.1) 0%,       /* Left binding shadow */
      hsla(0, 0%, 20%, 0.2) 0.5%,    /* Dark transition */
      hsla(0, 0%, 95%, 0.4) 0.8%,    /* Peak highlight */
      hsla(0, 0%, 85%, 0.4) 2.0%,    /* Peak highlight */
      hsla(0, 0%, 60%, 0.3) 2.8%,    /* Gradient highlight */
      hsla(0, 0%, 40%, 0.2) 4.2%,    /* Shadow between highlights */
      hsla(0, 0%, 70%, 0.15) 4.6%,   /* Second peak highlight */
      hsla(0, 0%, 60%, 0.15) 5.4%,   /* Second gradient highlight */
      hsla(0, 0%, 40%, 0.1) 6.0%,    /* Transition shadow */
      transparent 88%,                 /* Main book surface */
      hsla(0, 0%, 30%, 0.1) 99%,     /* Right edge highlight */
      hsla(0, 0%, 30%, 0.2) 99.5%,   /* Right edge shadow */
      hsla(0, 0%, 10%, 0.2) 100%     /* Right binding edge */
    );
  mask: linear-gradient(180deg,
    black 0%,
    rgba(0,0,0,0.9) 50%,
    rgba(0,0,0,0.5) 100%
  );
}
