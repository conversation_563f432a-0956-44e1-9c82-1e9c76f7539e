# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# certs and keys
/private_keys

# plists
Entitlements*.plist

# local confs
tauri.*.conf.json

# vercel
.vercel

# open-next
.open-next
.wrangler

# typescript
*.tsbuildinfo
next-env.d.ts

#generated
src-tauri/gen

# vendor
/public/vendor

# Auto Generated PWA files
/public/sw.js
/public/workbox-*.js
/public/fallback-*.js
/public/swe-worker-*.js

