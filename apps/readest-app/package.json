{"name": "@readest/readest-app", "version": "0.9.72", "private": true, "scripts": {"dev": "dotenv -e .env.tauri -- next dev", "build": "dotenv -e .env.tauri -- next build", "start": "dotenv -e .env.tauri -- next start", "dev-web": "dotenv -e .env.web -- next dev", "build-web": "dotenv -e .env.web -- next build", "start-web": "dotenv -e .env.web -- next start", "i18n:extract": "i18next-scanner", "lint": "next lint", "test": "dotenv -e .env -e .env.test.local vitest", "tauri": "tauri", "prepare-public-vendor": "mkdirp ./public/vendor/pdfjs", "copy-pdfjs-js": "cpx \"../../packages/foliate-js/node_modules/pdfjs-dist/legacy/build/{pdf.worker.min.mjs,pdf.mjs,pdf.d.mts}\" ./public/vendor/pdfjs", "copy-pdfjs-fonts": "cpx \"../../packages/foliate-js/node_modules/pdfjs-dist/{cmaps,standard_fonts}/*\" ./public/vendor/pdfjs", "copy-flatten-pdfjs-annotation-layer-css": "npx postcss \"../../packages/foliate-js/vendor/pdfjs/annotation_layer_builder.css\" --no-map -u postcss-nested > ./public/vendor/pdfjs/annotation_layer_builder.css", "copy-flatten-pdfjs-text-layer-css": "npx postcss \"../../packages/foliate-js/vendor/pdfjs/text_layer_builder.css\" --no-map -u postcss-nested > ./public/vendor/pdfjs/text_layer_builder.css", "copy-flatten-pdfjs-css": "pnpm copy-flatten-pdfjs-annotation-layer-css && pnpm copy-flatten-pdfjs-text-layer-css", "copy-pdfjs": "pnpm copy-pdfjs-js && pnpm copy-pdfjs-fonts && pnpm copy-flatten-pdfjs-css", "setup-pdfjs": "pnpm prepare-public-vendor && pnpm copy-pdfjs", "build-win-x64": "dotenv -e .env.tauri.local -- tauri build --target i686-pc-windows-msvc --bundles nsis", "build-win-arm64": "dotenv -e .env.tauri.local -- tauri build --target aarch64-pc-windows-msvc --bundles nsis", "build-linux-x64": "dotenv -e .env.tauri.local -- tauri build --target x86_64-unknown-linux-gnu --bundles appimage", "build-macos-universial": "dotenv -e .env.tauri.local -e .env.apple-nonstore.local -- tauri build -t universal-apple-darwin --bundles dmg", "build-macos-universial-appstore": "dotenv -e .env.tauri.local -e .env.apple-appstore.local -- tauri build -t universal-apple-darwin --bundles app --config src-tauri/tauri.appstore.conf.json", "build-macos-universial-appstore-dev": "dotenv -e .env.tauri.local -e .env.apple-appstore-dev.local -- tauri build -t universal-apple-darwin --bundles app --config src-tauri/tauri.appstore-dev.conf.json", "build-ios": "dotenv -e .env.ios-appstore-dev.local -- tauri ios build", "build-ios-appstore": "dotenv -e .env.ios-appstore.local -- tauri ios build --export-method app-store-connect", "release-macos-universial-appstore": "dotenv -e .env.tauri.local -e .env.apple-appstore.local -- bash scripts/release-mac-appstore.sh", "release-ios-appstore": "dotenv -e .env.ios-appstore.local -- bash scripts/release-ios-appstore.sh", "release-google-play": "dotenv -e .env.google-play.local -- bash scripts/release-google-play.sh", "config-wrangler": "sed -i \"s/\\${TRANSLATIONS_KV_ID}/$TRANSLATIONS_KV_ID/g\" wrangler.toml", "preview": "NEXT_PUBLIC_APP_PLATFORM=web opennextjs-cloudflare build && opennextjs-cloudflare preview --ip 0.0.0.0", "deploy": "NEXT_PUBLIC_APP_PLATFORM=web opennextjs-cloudflare build && opennextjs-cloudflare deploy", "upload": "NEXT_PUBLIC_APP_PLATFORM=web opennextjs-cloudflare build && opennextjs-cloudflare upload", "cf-typegen": "wrangler types --env-interface CloudflareEnv cloudflare-env.d.ts", "check:optional-chaining": "count=$(grep -rno '\\?\\.[a-zA-Z_$]' .next/static/chunks/* out/_next/static/chunks/* | wc -l); if [ \"$count\" -gt 0 ]; then echo '❌ Optional chaining found in output!'; exit 1; else echo '✅ No optional chaining found.'; fi", "check:translations": "count=$(grep -rno '__STRING_NOT_TRANSLATED__' public/locales/* | wc -l); if [ \"$count\" -gt 0 ]; then echo '❌ Untranslated strings found!'; exit 1; else echo '✅ All strings translated.'; fi", "check:all": "pnpm check:optional-chaining && pnpm check:translations", "build-check": "pnpm build && pnpm build-web && pnpm check:all"}, "dependencies": {"@aws-sdk/client-s3": "^3.735.0", "@aws-sdk/s3-request-presigner": "^3.735.0", "@ducanh2912/next-pwa": "^10.2.9", "@fabianlars/tauri-plugin-oauth": "2", "@opennextjs/cloudflare": "^1.6.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/supabase-js": "^2.50.2", "@tauri-apps/api": "2.6.0", "@tauri-apps/plugin-cli": "^2.4.0", "@tauri-apps/plugin-deep-link": "^2.4.0", "@tauri-apps/plugin-dialog": "^2.3.0", "@tauri-apps/plugin-fs": "^2.4.0", "@tauri-apps/plugin-haptics": "^2.3.0", "@tauri-apps/plugin-http": "^2.5.0", "@tauri-apps/plugin-log": "^2.6.0", "@tauri-apps/plugin-opener": "^2.4.0", "@tauri-apps/plugin-os": "^2.3.0", "@tauri-apps/plugin-process": "^2.3.0", "@tauri-apps/plugin-shell": "~2.3.0", "@tauri-apps/plugin-updater": "^2.9.0", "@zip.js/zip.js": "^2.7.53", "app-store-server-api": "^0.17.1", "aws4fetch": "^1.0.20", "clsx": "^2.1.1", "cors": "^2.8.5", "cssbeautify": "^0.3.1", "dayjs": "^1.11.13", "foliate-js": "workspace:*", "franc-min": "^6.2.0", "highlight.js": "^11.11.1", "i18next": "^24.2.0", "i18next-browser-languagedetector": "^8.0.2", "i18next-http-backend": "^3.0.1", "iso-639-2": "^3.0.2", "iso-639-3": "^3.0.1", "js-md5": "^0.8.3", "jwt-decode": "^4.0.0", "marked": "^15.0.12", "next": "15.3.3", "overlayscrollbars": "^2.11.4", "overlayscrollbars-react": "^0.5.6", "posthog-js": "^1.246.0", "react": "19.0.0", "react-color": "^2.19.3", "react-dom": "19.0.0", "react-i18next": "^15.2.0", "react-icons": "^5.4.0", "react-responsive": "^10.0.0", "react-window": "^1.8.11", "semver": "^7.7.1", "stripe": "^18.2.1", "tinycolor2": "^1.6.0", "uuid": "^11.1.0", "zod": "^4.0.8", "zustand": "5.0.6"}, "devDependencies": {"@next/bundle-analyzer": "^15.4.2", "@tailwindcss/typography": "^0.5.16", "@tauri-apps/cli": "2.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/cors": "^2.8.17", "@types/cssbeautify": "^0.3.5", "@types/node": "^22.15.31", "@types/react": "18.3.12", "@types/react-color": "^3.0.13", "@types/react-dom": "18.3.1", "@types/react-window": "^1.8.8", "@types/semver": "^7.7.0", "@types/tinycolor2": "^1.4.6", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.20", "cpx2": "^8.0.0", "daisyui": "^4.12.24", "dotenv-cli": "^7.4.4", "eslint": "^9.16.0", "eslint-config-next": "15.0.3", "i18next-scanner": "^4.6.0", "jsdom": "^26.1.0", "mkdirp": "^3.0.1", "node-env-run": "^4.0.2", "postcss": "^8.4.49", "postcss-cli": "^11.0.0", "postcss-nested": "^7.0.2", "raw-loader": "^4.0.2", "tailwindcss": "^3.4.17", "typescript": "^5.7.2", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4", "wrangler": "^4.26.0"}}