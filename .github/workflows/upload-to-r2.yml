name: Upload Release Assets to R2

on:
  workflow_call:
    inputs:
      tag:
        required: true
        type: string

  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag name (e.g., v1.2.3)'
        required: true
        type: string

jobs:
  upload-to-r2:
    runs-on: ubuntu-latest
    timeout-minutes: 3
    strategy:
      fail-fast: false
    env:
      RELEASE_R2_BUCKET: readest-releases
      RELEASE_R2_ACCOUNT_ID: ${{ secrets.RELEASE_R2_ACCOUNT_ID }}
      RELEASE_R2_ACCESS_KEY_ID: ${{ secrets.RELEASE_R2_ACCESS_KEY_ID }}
      RELEASE_R2_SECRET_ACCESS_KEY: ${{ secrets.RELEASE_R2_SECRET_ACCESS_KEY }}

    steps:
      - name: Download release assets
        run: |
          gh release download "${{ inputs.tag }}" --repo readest/readest --dir ./release-assets
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install rclone
        run: curl https://rclone.org/install.sh | sudo bash

      - name: Configure rclone
        run: |
          mkdir -p ~/.config/rclone
          cat > ~/.config/rclone/rclone.conf <<EOF
          [r2]
          type = s3
          provider = Cloudflare
          access_key_id = $RELEASE_R2_ACCESS_KEY_ID
          secret_access_key = $RELEASE_R2_SECRET_ACCESS_KEY
          endpoint = https://${RELEASE_R2_ACCOUNT_ID}.r2.cloudflarestorage.com
          EOF

      - name: Modify latest.json download URLs
        run: |
          GITHUB_BASE_URL="https://github.com/readest/readest/releases/download"
          READEST_BASE_URL="https://download.readest.com/releases"
          sed -i "s#${GITHUB_BASE_URL}#${READEST_BASE_URL}#g" ./release-assets/latest.json

      - name: Upload to R2
        run: |
          mkdir releases
          mv ./release-assets/latest.json releases
          mv ./release-assets/release-notes.json releases
          rclone copy ./release-assets r2:${RELEASE_R2_BUCKET}/releases/${{ inputs.tag }}/
          rclone copy ./releases r2:${RELEASE_R2_BUCKET}/releases/

      - name: Upload successful
        if: success()
        run: echo "Upload completed successfully"

  retry-on-failure:
    if: failure() && fromJSON(github.run_attempt) < 3
    needs: [upload-to-r2]
    runs-on: ubuntu-latest
    steps:
      - env:
          GH_REPO: ${{ github.repository }}
          GH_TOKEN: ${{ github.token }}
        run: gh workflow run retry-workflow.yml -F run_id=${{ github.run_id }}
