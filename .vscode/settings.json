{
  "typescript.tsdk": "node_modules/typescript/lib",
  "rust-analyzer.linkedProjects": [
    "packages/tauri/Cargo.toml",
    "apps/readest-app/src-tauri/Cargo.toml"
  ],
  // "editor.formatOnSave": true, // uncomment to add format on save
  "typescript.inlayHints.parameterNames.enabled": "all",
  "typescript.inlayHints.variableTypes.enabled": true,
  "typescript.inlayHints.propertyDeclarationTypes.enabled": true,
  "typescript.inlayHints.functionLikeReturnTypes.enabled": true,
  "typescript.inlayHints.enumMemberValues.enabled": true,
  "javascript.validate.enable": false,
  "javascript.format.enable": false,
  "typescript.format.enable": false,
}